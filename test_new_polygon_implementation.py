#!/usr/bin/env python3
"""
测试新的多边形禁飞区实现（不使用clipper库）
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from pathfinding_cpp import Map3D, create_grid_converter, OccupancyMap
    print("✓ 成功导入 pathfinding_cpp 模块")
except ImportError as e:
    print(f"✗ 导入 pathfinding_cpp 模块失败: {e}")
    print("请确保已正确编译 C++ 扩展模块")
    sys.exit(1)

def test_polygon_implementations():
    """测试新旧两种多边形实现"""
    
    # 创建网格转换器
    converter = create_grid_converter(
        lat_size=0.001,  # 纬度网格大小（度）
        lon_size=0.001,  # 经度网格大小（度）
        alt_size=100.0,  # 高度网格大小（米）
        min_lat=39.0,    # 最小纬度
        min_lon=116.0,   # 最小经度
        min_alt=0.0      # 最小高度
    )
    
    # 创建地图
    map_3d = Map3D(
        lat_size=200,    # 纬度网格数量
        lon_size=200,    # 经度网格数量
        alt_size=50,     # 高度网格数量
        converter=converter
    )
    
    # 创建占用地图
    occupancy_map = OccupancyMap(200, 200, 50)
    
    print("✓ 成功创建地图和占用地图")
    
    # 定义测试多边形（正方形）
    polygon_vertices = [
        (50.0, 50.0),   # 左下角
        (70.0, 50.0),   # 右下角
        (70.0, 70.0),   # 右上角
        (50.0, 70.0)    # 左上角
    ]
    
    offset_grids = 2.0
    boundary_thickness = 2
    planned_paths = {}  # 空的已规划路径
    
    print(f"测试多边形顶点: {polygon_vertices}")
    print(f"偏移距离: {offset_grids} 网格")
    print(f"边界厚度: {boundary_thickness} 网格")
    
    # 测试新实现（不使用clipper）
    print("\n=== 测试新实现（不使用clipper） ===")
    try:
        success, conflicts = map_3d.add_hollow_polygonal_no_fly_zone_grid(
            polygon_vertices,
            "test_zone_new",
            offset_grids,
            boundary_thickness,
            planned_paths
        )
        
        if success:
            print("✓ 新实现成功添加多边形禁飞区")
            print(f"  冲突航班数量: {len(conflicts)}")
        else:
            print("✗ 新实现添加多边形禁飞区失败")
            
    except Exception as e:
        print(f"✗ 新实现出现异常: {e}")
    
    # 测试旧实现（使用clipper）
    print("\n=== 测试旧实现（使用clipper） ===")
    try:
        success, conflicts = map_3d.add_hollow_polygonal_no_fly_zone_grid_clipper(
            polygon_vertices,
            "test_zone_clipper",
            offset_grids,
            boundary_thickness,
            planned_paths
        )
        
        if success:
            print("✓ 旧实现成功添加多边形禁飞区")
            print(f"  冲突航班数量: {len(conflicts)}")
        else:
            print("✗ 旧实现添加多边形禁飞区失败")
            
    except Exception as e:
        print(f"✗ 旧实现出现异常: {e}")
    
    # 测试点是否在禁飞区内
    print("\n=== 测试点检查功能 ===")
    test_points = [
        (60, 60),  # 应该在禁飞区内
        (30, 30),  # 应该在禁飞区外
        (52, 52),  # 边界附近
    ]
    
    for point in test_points:
        try:
            nfz_name = map_3d.is_point_inside_any_nfz_2d(point)
            if nfz_name:
                print(f"  点 {point} 在禁飞区 '{nfz_name}' 内")
            else:
                print(f"  点 {point} 不在任何禁飞区内")
        except Exception as e:
            print(f"  检查点 {point} 时出现异常: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_polygon_implementations()
