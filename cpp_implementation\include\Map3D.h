// 三维地图类定义
#pragma once

#include "common_types.h"        // 引入基础类型定义
#include "GridConverter.h"       // 引入地理坐标转换器
#include "ObstacleTypeManager.h" // 引入障碍物管理器
#include "GridNode3D.h"          // 引入三维栅格节点定义
#include <unordered_set>         // 用于不可通行节点集合
#include <vector>                // 用于存储顶点等数据
#include <string>                // 用于字符串处理
#include <cmath>                 // 用于数学计算（如std::round等）
#include <set>                   // 用于临时储存栅格单元
#include <ctime>                 // 用于时间戳生成
#include <map>                   // 用于路径映射等

// 备注：tuple和functional（用于Point3DHash）已通过common_types.h间接包含
// 地理相关常量（地球半径、圆周率、角度转弧度等）已移至GridConverter.h

// Point3DHash哈希函数结构体的定义已移至common_types.h

class Map3D
{
public:
    // 三维地图构造函数
    // @param lat_size: 地图在纬度方向上的栅格数量 (对应Y轴/南北方向)
    // @param lon_size: 地图在经度方向上的栅格数量 (对应X轴/东西方向)
    // @param alt_size: 地图在高度方向上的栅格数量 (对应Z轴/垂直方向)
    // @param converter: 用于地理坐标和栅格坐标转换的工具实例
    Map3D(int lat_size, int lon_size, int alt_size, const GridConverter &converter)
        : lat_size_(lat_size),
          lon_size_(lon_size),
          alt_size_(alt_size),
          converter_(converter),                           // 初始化坐标转换器引用
          obstacle_manager_(lat_size, lon_size, alt_size), // 初始化障碍物管理器
          non_traversable_nodes_()                         // 初始化不可通行节点集合
    {
        // 可以在构造函数中初始化不可通行节点列表
        // 或通过update_internal_obstacles()方法更新
    }

    // 检查指定坐标是否在地图边界内
    // @param lon_idx: 经度方向栅格坐标 (对应X轴/东西方向)
    // @param lat_idx: 纬度方向栅格坐标 (对应Y轴/南北方向)
    // @param alt_idx: 高度方向栅格坐标 (对应Z轴/垂直方向)
    // @return: 如果坐标在边界内返回true，否则返回false
    bool is_within_bounds(int lon_idx, int lat_idx, int alt_idx) const
    {
        // 检查坐标是否在地图边界内
        if (lon_idx < 0 || lon_idx >= lon_size_ || lat_idx < 0 || lat_idx >= lat_size_ || alt_idx < 0 || alt_idx >= alt_size_)
        {
            return false; // 超出地图范围的点视为不可通行
        }
        return true; // 在边界内
    }

    // 检查指定栅格坐标点是否可以通行
    // 注意：此函数假定坐标已通过 is_within_bounds 检查或调用者负责边界检查
    // @param lon_idx: 经度方向栅格坐标 (对应X轴/东西方向)
    // @param lat_idx: 纬度方向栅格坐标 (对应Y轴/南北方向)
    // @param alt_idx: 高度方向栅格坐标 (对应Z轴/垂直方向)
    // @return: 如果该点在不可通行节点集合中返回false（不可通行），否则返回true（可通行）
    bool is_traversable(int lon_idx, int lat_idx, int alt_idx) const
    {
        // 检查该点是否在不可通行节点集合中
        return non_traversable_nodes_.find(std::make_tuple(lon_idx, lat_idx, alt_idx)) == non_traversable_nodes_.end();
    }

    // 检查指定三维点是否可以通行（重载版本）
    // 注意：此函数假定坐标已通过 is_within_bounds 检查或调用者负责边界检查
    // @param node: 待检查的三维栅格点 (lon_idx, lat_idx, alt_idx)
    // @return: 如果该点在不可通行节点集合中返回false（不可通行），否则返回true（可通行）
    bool is_traversable(const Point3D &node) const
    {
        // 检查该点是否在不可通行节点集合中
        return non_traversable_nodes_.find(node) == non_traversable_nodes_.end();
    }

    // 获取地图在各个方向上的尺寸
    // @return: 分别返回地图的纬度、经度和高度方向栅格数
    int get_lat_size() const { return lat_size_; } // 纬度方向栅格数 (对应Y轴/南北方向)
    int get_lon_size() const { return lon_size_; } // 经度方向栅格数 (对应X轴/东西方向)
    int get_alt_size() const { return alt_size_; } // 高度方向栅格数 (对应Z轴/垂直方向)

    // 为了向后兼容，保留旧的方法名（但建议使用新的方法名）
    int get_height() const { return lat_size_; } // 已弃用：请使用 get_lat_size()
    int get_width() const { return lon_size_; }  // 已弃用：请使用 get_lon_size()
    int get_depth() const { return alt_size_; }  // 已弃用：请使用 get_alt_size()

    // 获取障碍物管理器的引用
    // @return: 返回障碍物管理器的引用，允许外部代码添加和管理障碍物
    ObstacleTypeManager &get_obstacle_manager()
    {
        return obstacle_manager_;
    }

    // 获取障碍物管理器的常量引用
    // @return: 返回障碍物管理器的常量引用，只允许读取操作
    const ObstacleTypeManager &get_obstacle_manager() const
    {
        return obstacle_manager_;
    }

    // 获取坐标转换器的引用
    // @return: 返回坐标转换器的常量引用
    const GridConverter &get_converter() const
    {
        return converter_;
    }

    // 从障碍物管理器更新地图的不可通行节点集合
    // 该方法会同步障碍物管理器中的最新状态到地图的内部表示中
    void update_internal_obstacles()
    {
        non_traversable_nodes_ = obstacle_manager_.get_all_non_traversable_nodes();
    }

    // 添加基于地理坐标的圆柱形禁飞区
    // @param center_lat_deg: 禁飞区中心点纬度（度）
    // @param center_lon_deg: 禁飞区中心点经度（度）
    // @param radius_meters: 禁飞区半径（米）
    // @param zone_name: 禁飞区名称
    // @param buffer_distance_meters: 缓冲区大小（米）
    // @param planned_paths_map: 已规划的航路信息
    // @param boundary_thickness_grids: 禁飞区边界厚度（栅格数）
    // @param conflicting_flight_ids: 输出参数，存储与新禁飞区冲突的航班ID
    // @return: 返回一个pair，第一个元素表示是否成功，第二个元素是冲突的航班ID列表
    std::pair<bool, std::vector<std::string>> add_solid_cylindrical_no_fly_zone(
        double center_lat_deg, double center_lon_deg, double radius_meters,
        const std::string &zone_name,
        int buffer_distance_meters,
        const std::map<std::string, std::vector<GridNode3D>> &planned_paths_map,
        int boundary_thickness_grids);

    // 添加基于栅格坐标的圆柱形禁飞区
    // @param center_lon_grid: 禁飞区中心点经度坐标（栅格，对应X轴）
    // @param center_lat_grid: 禁飞区中心点纬度坐标（栅格，对应Y轴）
    // @param radius_in_grids: 禁飞区半径（栅格数）
    // @param zone_name: 禁飞区名称
    // @param planned_paths_map: 已规划的航路信息
    // @param boundary_thickness_grids: 禁飞区边界厚度（栅格数）
    // @return: 返回一个pair，第一个元素表示是否成功，第二个元素是冲突的航班ID列表
    std::pair<bool, std::vector<std::string>> add_solid_cylindrical_no_fly_zone_grid(
        float center_lon_grid, float center_lat_grid,
        float radius_in_grids,
        const std::string &zone_name,
        const std::map<std::string, std::vector<GridNode3D>> &planned_paths_map,
        int boundary_thickness_grids);

    // 删除指定名称的禁飞区
    // @param zone_name: 要删除的禁飞区名称
    // @return: 删除成功或禁飞区不存在时返回true，删除过程出错返回false
    bool remove_no_fly_zone(const std::string &zone_name);

    // 添加空心多边形禁飞区
    // @param polygon_vertices_geo: 多边形顶点的地理坐标列表
    // @param zone_name: 禁飞区名称
    // @param offset_meters_b: 几何外扩距离（米）
    // @param boundary_thickness_grids: 边界厚度（栅格数，必须>=1）
    // @param planned_paths_map: 已规划的航路信息
    // @return: 返回一个pair，第一个元素表示是否成功，第二个元素是冲突的航班ID列表
    std::pair<bool, std::vector<std::string>> add_hollow_polygonal_no_fly_zone(
        const std::vector<GeoCoordinate> &polygon_vertices_geo,
        const std::string &zone_name,
        double offset_meters_b,
        int boundary_thickness_grids,
        const std::map<std::string, std::vector<GridNode3D>> &planned_paths_map);

    // 添加基于栅格坐标的空心多边形禁飞区（新版本，不使用clipper）
    // @param polygon_vertices_grid: 多边形顶点的栅格坐标列表
    // @param zone_name: 禁飞区名称
    // @param offset_grids: 几何外扩距离（栅格数）
    // @param boundary_thickness_grids: 边界厚度（栅格数，必须>=1）
    // @param planned_paths_map: 已规划的航路信息
    // @return: 返回一个pair，第一个元素表示是否成功，第二个元素是冲突的航班ID列表
    std::pair<bool, std::vector<std::string>> add_hollow_polygonal_no_fly_zone_grid(
        const std::vector<std::pair<float, float>> &polygon_vertices_grid,
        const std::string &zone_name,
        float offset_grids,
        int boundary_thickness_grids,
        const std::map<std::string, std::vector<GridNode3D>> &planned_paths_map);

    // 添加基于栅格坐标的空心多边形禁飞区（使用clipper库的版本）
    // @param polygon_vertices_grid: 多边形顶点的栅格坐标列表
    // @param zone_name: 禁飞区名称
    // @param offset_grids: 几何外扩距离（栅格数）
    // @param boundary_thickness_grids: 边界厚度（栅格数，必须>=1）
    // @param planned_paths_map: 已规划的航路信息
    // @return: 返回一个pair，第一个元素表示是否成功，第二个元素是冲突的航班ID列表
    std::pair<bool, std::vector<std::string>> add_hollow_polygonal_no_fly_zone_grid_clipper(
        const std::vector<std::pair<float, float>> &polygon_vertices_grid,
        const std::string &zone_name,
        float offset_grids,
        int boundary_thickness_grids,
        const std::map<std::string, std::vector<GridNode3D>> &planned_paths_map);

    // 检查指定二维点是否位于任何禁飞区内
    // @param point_lat_lon: 要检查的点的 (lat_idx, lon_idx) 网格坐标
    // @return: 如果点位于任何NFZ内部，则返回该NFZ的名称，及时break；如果点不在任何NFZ内部，则返回空字符串
    std::string is_point_inside_any_nfz_2d(const std::pair<int, int> &point_lat_lon) const;

    // 调试函数：获取禁飞区几何信息
    std::string debug_nfz_geometry_info() const;

private:
    // 检查禁飞区栅格单元与已规划航路的冲突情况
    // @param zone_cells: 禁飞区包含的栅格单元集合
    // @param planned_paths_map: 已规划的航路信息
    // @param conflicting_flight_ids_out: 输出参数，存储发生冲突的航班ID
    void check_path_conflicts(
        const std::set<Point3D> &zone_cells,
        const std::map<std::string, std::vector<GridNode3D>> &planned_paths_map,
        std::vector<std::string> &conflicting_flight_ids_out) const;

    // 注：地图相关常量（地球半径、圆周率等）已移至GridConverter类中

    // 地图尺寸
    int lat_size_; // 纬度方向栅格数 (对应Y轴/南北方向)
    int lon_size_; // 经度方向栅格数 (对应X轴/东西方向)
    int alt_size_; // 高度方向栅格数 (对应Z轴/垂直方向)

    // 核心组件
    const GridConverter &converter_;       // 坐标转换器
    ObstacleTypeManager obstacle_manager_; // 障碍物管理器

    // 不可通行节点集合（使用Point3DHash计算哈希值）
    // Point3D格式：(lon_idx, lat_idx, alt_idx)
    std::unordered_set<Point3D, Point3DHash> non_traversable_nodes_;
};
