// 三维地图实现文件
#include "Map3D.h"
#include "GridConverter.h"       // 引入坐标转换器
#include "ObstacleTypeManager.h" // 引入障碍物管理器
#include "GridNode3D.h"          // 引入路径规划节点
#include "common_types.h"        // 引入基础类型定义

#include <cmath>              // 引入数学函数（sqrt、round、floor、ceil、abs等）
#include <vector>             // 用于存储数据
#include <string>             // 用于字符串处理
#include <set>                // 用于存储区域单元
#include <stdexcept>          // 用于异常处理
#include <iomanip>            // 用于时间格式化输出
#include <sstream>            // 用于字符串流处理
#include <chrono>             // 用于时间戳生成
#include <algorithm>          // 用于min、max函数
#include "clipper2/clipper.h" // 引入多边形裁剪库

// 线段光栅化辅助函数
// @brief 将一条浮点数坐标的线段转换为经过的所有栅格单元集合
// @param x1,y1: 线段起点坐标
// @param x2,y2: 线段终点坐标
// @return: 返回线段经过的所有栅格单元坐标集合
static std::set<std::pair<int, int>> rasterize_line_segment(float x1, float y1, float x2, float y2)
{
    std::set<std::pair<int, int>> cells; // 存储经过的栅格单元
    float dx = x2 - x1;                  // X方向增量
    float dy = y2 - y1;                  // Y方向增量

    // 使用DDA算法计算采样步数
    // 确保即使是很短的线段或单点也至少有一个栅格单元
    int steps = static_cast<int>(std::max(1.0f, std::max(std::abs(dx), std::abs(dy))));

    // 计算每一步的坐标增量
    float x_inc = dx / static_cast<float>(steps); // X方向每步增量
    float y_inc = dy / static_cast<float>(steps); // Y方向每步增量

    // 初始化当前位置
    float current_x = x1;
    float current_y = y1;

    // 遍历线段上的所有采样点
    for (int i = 0; i <= steps; ++i)
    {
        cells.insert({static_cast<int>(std::round(current_x)),
                      static_cast<int>(std::round(current_y))}); // 将浮点坐标转换为栅格索引
        current_x += x_inc;                                      // 更新X坐标
        current_y += y_inc;                                      // 更新Y坐标
    }
    return cells; // 返回所有经过的栅格单元
}

// 时间戳生成辅助函数
// 生成当前时间的格式化字符串（格式：YYYY-MM-DD HH:MM:SS）
// 可以作为静态成员或匿名命名空间中的自由函数
namespace
{ // 匿名命名空间开始
    std::string generate_current_timestamp_string()
    {
        // 获取当前系统时间点
        auto now = std::chrono::system_clock::now();
        // 转换为时间戳
        auto in_time_t = std::chrono::system_clock::to_time_t(now);
        std::tm buf;

#ifdef _WIN32
        localtime_s(&buf, &in_time_t); // Windows平台使用线程安全版本
#else
        localtime_r(&in_time_t, &buf); // POSIX平台使用可重入版本
#endif
        // 格式化时间字符串
        std::ostringstream ss;
        ss << std::put_time(&buf, "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }
} // 匿名命名空间结束

// 多边形扩展辅助函数
// @brief 将多边形向外扩展指定的距离，用于创建安全缓冲区
// @param polygon: 多边形顶点坐标数组，每个元素是(x, y)格式
// @param expand_distance: 安全缓冲区距离（网格单位）
// @return: 扩展后的多边形顶点坐标数组
static std::vector<std::pair<float, float>> expand_polygon(
    const std::vector<std::pair<float, float>> &polygon,
    float expand_distance)
{
    if (expand_distance <= 0 || polygon.size() < 3)
    {
        return polygon;
    }

    size_t n_vertices = polygon.size();

    // 计算多边形的面积，用于确定多边形的旋转方向
    float area = 0;
    for (size_t i = 0; i < n_vertices; ++i)
    {
        size_t j = (i + 1) % n_vertices;
        area += polygon[i].first * polygon[j].second;
        area -= polygon[j].first * polygon[i].second;
    }
    area = area / 2.0f;

    // 确定法线方向的符号，保证向外扩展
    // 如果面积为正（逆时针），法线方向为(dy, -dx)
    // 如果面积为负（顺时针），法线方向为(-dy, dx)
    float normal_sign = (area > 0) ? -1.0f : 1.0f;

    // 创建扩展后的多边形
    std::vector<std::pair<float, float>> expanded_polygon;
    expanded_polygon.reserve(n_vertices);

    // 对每个顶点，计算其法线并沿法线方向扩展
    for (size_t i = 0; i < n_vertices; ++i)
    {
        // 获取当前顶点及其相邻顶点
        size_t prev_idx = (i - 1 + n_vertices) % n_vertices;
        size_t curr_idx = i;
        size_t next_idx = (i + 1) % n_vertices;

        const auto &prev_point = polygon[prev_idx];
        const auto &curr_point = polygon[curr_idx];
        const auto &next_point = polygon[next_idx];

        // 计算当前顶点的两个相邻边的向量
        float prev_edge_x = curr_point.first - prev_point.first;
        float prev_edge_y = curr_point.second - prev_point.second;
        float next_edge_x = next_point.first - curr_point.first;
        float next_edge_y = next_point.second - curr_point.second;

        // 将向量归一化
        float prev_edge_length = std::sqrt(prev_edge_x * prev_edge_x + prev_edge_y * prev_edge_y);
        float next_edge_length = std::sqrt(next_edge_x * next_edge_x + next_edge_y * next_edge_y);

        if (prev_edge_length > 0 && next_edge_length > 0)
        {
            float prev_edge_norm_x = prev_edge_x / prev_edge_length;
            float prev_edge_norm_y = prev_edge_y / prev_edge_length;
            float next_edge_norm_x = next_edge_x / next_edge_length;
            float next_edge_norm_y = next_edge_y / next_edge_length;

            // 计算两个边的法线（垂直于边的向量）
            // 根据多边形的旋转方向确定法线方向
            float prev_normal_x = normal_sign * (-prev_edge_norm_y);
            float prev_normal_y = normal_sign * prev_edge_norm_x;
            float next_normal_x = normal_sign * (-next_edge_norm_y);
            float next_normal_y = normal_sign * next_edge_norm_x;

            // 将两个法线向量相加并归一化，得到角平分线方向
            float bisector_x = prev_normal_x + next_normal_x;
            float bisector_y = prev_normal_y + next_normal_y;
            float bisector_length = std::sqrt(bisector_x * bisector_x + bisector_y * bisector_y);

            if (bisector_length > 0)
            {
                float bisector_norm_x = bisector_x / bisector_length;
                float bisector_norm_y = bisector_y / bisector_length;

                // 计算角平分线与法线之间的角度
                // 使用点积计算余弦值
                float cos_angle = bisector_norm_x * prev_normal_x + bisector_norm_y * prev_normal_y;
                // 防止除以0
                if (std::abs(cos_angle) < 1e-10f)
                {
                    cos_angle = 1e-10f;
                }

                // 计算扩展距离（考虑角度因素）
                // 当角度越小，需要的扩展距离越大
                float adjusted_distance = expand_distance / std::abs(cos_angle);
                // 限制最大扩展距离，防止在尖角处扩展过大
                adjusted_distance = std::min(adjusted_distance, expand_distance * 3.0f);

                // 沿角平分线方向扩展顶点
                float expanded_x = curr_point.first + bisector_norm_x * adjusted_distance;
                float expanded_y = curr_point.second + bisector_norm_y * adjusted_distance;
                expanded_polygon.push_back({expanded_x, expanded_y});
            }
            else
            {
                // 如果无法计算角平分线，使用简单的向外扩展
                // 计算多边形的中心点
                float center_x = 0, center_y = 0;
                for (const auto &pt : polygon)
                {
                    center_x += pt.first;
                    center_y += pt.second;
                }
                center_x /= static_cast<float>(polygon.size());
                center_y /= static_cast<float>(polygon.size());

                float vector_x = curr_point.first - center_x;
                float vector_y = curr_point.second - center_y;
                float vector_length = std::sqrt(vector_x * vector_x + vector_y * vector_y);
                if (vector_length > 0)
                {
                    float expanded_x = curr_point.first + (vector_x / vector_length) * expand_distance;
                    float expanded_y = curr_point.second + (vector_y / vector_length) * expand_distance;
                    expanded_polygon.push_back({expanded_x, expanded_y});
                }
                else
                {
                    expanded_polygon.push_back(curr_point);
                }
            }
        }
        else
        {
            // 如果边长度为0，使用简单的向外扩展
            // 计算多边形的中心点
            float center_x = 0, center_y = 0;
            for (const auto &pt : polygon)
            {
                center_x += pt.first;
                center_y += pt.second;
            }
            center_x /= static_cast<float>(polygon.size());
            center_y /= static_cast<float>(polygon.size());

            float vector_x = curr_point.first - center_x;
            float vector_y = curr_point.second - center_y;
            float vector_length = std::sqrt(vector_x * vector_x + vector_y * vector_y);
            if (vector_length > 0)
            {
                float expanded_x = curr_point.first + (vector_x / vector_length) * expand_distance;
                float expanded_y = curr_point.second + (vector_y / vector_length) * expand_distance;
                expanded_polygon.push_back({expanded_x, expanded_y});
            }
            else
            {
                expanded_polygon.push_back(curr_point);
            }
        }
    }

    return expanded_polygon;
}

// 提取多边形边界点辅助函数
// @brief 提取多边形的边界点，生成一个具有指定厚度的边界
// @param polygon: 多边形顶点坐标，每个元素是(x, y)格式
// @param boundary_thickness: 边界厚度（网格单位）
// @return: 边界点坐标数组
static std::vector<std::pair<int, int>> extract_polygon_boundary(
    const std::vector<std::pair<float, float>> &polygon,
    int boundary_thickness)
{
    if (polygon.size() < 3 || boundary_thickness < 1)
    {
        return {};
    }

    // 确保多边形是闭合的（首尾相连）
    std::vector<std::pair<float, float>> closed_polygon = polygon;
    if (polygon.size() > 0 &&
        (std::abs(polygon[0].first - polygon.back().first) > 1e-6f ||
         std::abs(polygon[0].second - polygon.back().second) > 1e-6f))
    {
        closed_polygon.push_back(polygon[0]);
    }

    // 计算多边形的边界框
    float y_min = closed_polygon[0].second, x_min = closed_polygon[0].first;
    float y_max = closed_polygon[0].second, x_max = closed_polygon[0].first;
    for (const auto &pt : closed_polygon)
    {
        y_min = std::min(y_min, pt.second);
        y_max = std::max(y_max, pt.second);
        x_min = std::min(x_min, pt.first);
        x_max = std::max(x_max, pt.first);
    }

    int y_min_int = static_cast<int>(std::floor(y_min));
    int y_max_int = static_cast<int>(std::ceil(y_max));
    int x_min_int = static_cast<int>(std::floor(x_min));
    int x_max_int = static_cast<int>(std::ceil(x_max));

    // 存储所有边界点的集合
    std::set<std::pair<int, int>> all_boundary_points;

    for (size_t i = 0; i < closed_polygon.size() - 1; ++i)
    {
        const auto &p1 = closed_polygon[i];
        const auto &p2 = closed_polygon[i + 1];

        // 计算边的长度
        float dx = p2.first - p1.first;
        float dy = p2.second - p1.second;
        float length = std::sqrt(dx * dx + dy * dy);

        // 根据边的长度确定采样点数量（至少2个点）
        int num_samples = std::max(2, static_cast<int>(length * 2));

        // 在边上均匀采样
        for (int j = 0; j <= num_samples; ++j)
        {
            float t = static_cast<float>(j) / static_cast<float>(num_samples);
            float x = p1.first + t * dx;
            float y = p1.second + t * dy;

            int y_int = static_cast<int>(std::round(y));
            int x_int = static_cast<int>(std::round(x));

            // 为每个采样点创建偏移网格
            for (int oy = -boundary_thickness; oy <= boundary_thickness; ++oy)
            {
                for (int ox = -boundary_thickness; ox <= boundary_thickness; ++ox)
                {
                    // 计算曼哈顿距离
                    int manhattan_dist = std::abs(ox) + std::abs(oy);

                    // 找出在边界厚度内的点
                    if (manhattan_dist <= boundary_thickness)
                    {
                        int final_y = y_int + oy;
                        int final_x = x_int + ox;

                        // 筛选在边界框内的点
                        if (final_y >= y_min_int && final_y <= y_max_int &&
                            final_x >= x_min_int && final_x <= x_max_int)
                        {
                            all_boundary_points.insert({final_y, final_x});
                        }
                    }
                }
            }
        }
    }

    // 转换为vector返回
    std::vector<std::pair<int, int>> result;
    result.reserve(all_boundary_points.size());
    for (const auto &pt : all_boundary_points)
    {
        result.push_back(pt);
    }

    return result;
}

// 生成3D边界点辅助函数
// @brief 根据2D边界点生成3D边界点集合
// @param boundary_points_2d: 2D边界点坐标数组
// @param max_height: 最大高度
// @return: 3D边界点集合
static std::set<Point3D> generate_boundary_points_3d(
    const std::vector<std::pair<int, int>> &boundary_points_2d,
    int max_height)
{
    std::set<Point3D> boundary_points_3d;

    // 为每个2D点创建所有高度的组合
    for (const auto &pt_2d : boundary_points_2d)
    {
        for (int z = 0; z < max_height; ++z)
        {
            boundary_points_3d.insert({pt_2d.second, pt_2d.first, z}); // 注意：(x,y,z) = (lon,lat,alt)
        }
    }

    return boundary_points_3d;
}

// 添加基于栅格坐标的圆柱形禁飞区
// @brief 在指定栅格位置添加一个圆柱形的禁飞区域
// @param center_lon_grid: 圆柱中心经度坐标（栅格，对应X轴）
// @param center_lat_grid: 圆柱中心纬度坐标（栅格，对应Y轴）
// @param radius_in_grids: 圆柱半径（栅格数）
// @param zone_name: 禁飞区名称
// @param planned_paths_map: 已规划的航路信息
// @param boundary_thickness_grids: 边界厚度（栅格数）
// @param conflicting_flight_ids: 输出参数，存储与新禁飞区冲突的航班ID
// @return: 添加成功返回true，否则返回false
std::pair<bool, std::vector<std::string>> Map3D::add_solid_cylindrical_no_fly_zone_grid(
    float center_lon_grid, float center_lat_grid,
    float radius_in_grids,
    const std::string &zone_name,
    const std::map<std::string, std::vector<GridNode3D>> &planned_paths_map,
    int boundary_thickness_grids)
{
    std::vector<std::string> conflicting_flight_ids;
    // 清空输出参数
    conflicting_flight_ids.clear();

    // 检查禁飞区是否已存在
    bool type_existed_initially = false;
    try
    {
        obstacle_manager_.get_type_info(zone_name);
        type_existed_initially = true;
    }
    catch (const std::runtime_error &)
    {
        // 禁飞区类型不存在，继续创建
    }

    // 如果禁飞区已存在，直接返回成功
    if (type_existed_initially)
    {
        return std::make_pair(true, conflicting_flight_ids);
    }

    // 创建存储禁飞区栅格单元的集合
    std::set<Point3D> zone_cells;

    // 计算圆柱体的有效边界范围
    // 在半径基础上增加0.5以确保边界栅格的覆盖
    float effective_radius = radius_in_grids + 0.5f;

    // 计算包围圆柱体的矩形范围
    int min_lon_idx = static_cast<int>(std::floor(center_lon_grid - effective_radius));
    int max_lon_idx = static_cast<int>(std::ceil(center_lon_grid + effective_radius));
    int min_lat_idx = static_cast<int>(std::floor(center_lat_grid - effective_radius));
    int max_lat_idx = static_cast<int>(std::ceil(center_lat_grid + effective_radius));

    // 确保边界在地图范围内
    min_lon_idx = std::max(0, min_lon_idx);
    max_lon_idx = std::min(lon_size_ - 1, max_lon_idx);
    min_lat_idx = std::max(0, min_lat_idx);
    max_lat_idx = std::min(lat_size_ - 1, max_lat_idx);

    // 计算圆柱体的内外半径
    float outer_radius_sq = radius_in_grids * radius_in_grids;
    // 如果边界厚度大于0，则计算内圆半径
    // 厚度N表示从R-N到R的范围都是边界
    // 如果内半径小于等于0，则表示整个圆柱体都是实心的
    float inner_radius = radius_in_grids - static_cast<float>(boundary_thickness_grids);
    float inner_radius_sq = (inner_radius > 0.0f) ? (inner_radius * inner_radius) : -1.0f;

    // 遍历包围盒中的所有栅格，检查是否在圆柱体范围内
    for (int lat_idx = min_lat_idx; lat_idx <= max_lat_idx; ++lat_idx)
    {
        for (int lon_idx = min_lon_idx; lon_idx <= max_lon_idx; ++lon_idx)
        {
            // 计算栅格中心到圆柱中心的距离
            float dlon = static_cast<float>(lon_idx) - center_lon_grid;
            float dlat = static_cast<float>(lat_idx) - center_lat_grid;
            float dist_sq = dlon * dlon + dlat * dlat; // 距离平方

            // 判断该栅格是否在圆柱体边界内
            bool in_boundary = false;
            if (boundary_thickness_grids <= 0)
            {
                // 如果边界厚度非正数，则为实心圆柱体
                in_boundary = dist_sq <= outer_radius_sq;
            }
            else
            {
                // 否则判断是否在外圆内且不在内圆内（如果有内圆的话）
                in_boundary = (dist_sq <= outer_radius_sq) &&
                              (inner_radius_sq < 0.0f || dist_sq >= inner_radius_sq);
            }

            // 如果在边界内，将该栅格的所有高度层都加入禁飞区
            if (in_boundary)
            {
                for (int alt_idx = 0; alt_idx < alt_size_; ++alt_idx)
                {
                    zone_cells.insert({lon_idx, lat_idx, alt_idx});
                }
            }
        }
    }

    // 检查是否有成功生成禁飞区栅格
    if (zone_cells.empty())
    {
        // 未生成任何栅格，可能是半径太小或位置超出边界
        // 这里可以考虑添加日志记录
        return std::make_pair(true, conflicting_flight_ids); // 返回true表示空区域也算成功添加
    }

    // 检查与已有航路的冲突
    check_path_conflicts(zone_cells, planned_paths_map, conflicting_flight_ids);

    // 将禁飞区添加到障碍物管理器
    try
    {
        // 创建圆柱形禁飞区几何信息
        auto geometry = std::make_unique<CylindricalNFZGeometry>(center_lon_grid, center_lat_grid, radius_in_grids);

        // 添加禁飞区类型（带几何信息）
        obstacle_manager_.add_nfz_type(zone_name,
                                       "Cylindrical No-Fly Zone (Grid)",
                                       generate_current_timestamp_string(),
                                       std::move(geometry));
    }
    catch (const std::runtime_error &e)
    {
        // 可能由于并发操作，其他线程/进程已经添加了该类型
        // 或者add_nfz_type本身因其他原因失败
        // 这里假设如果之前的检查通过（类型不存在），那么添加应该成功
        // 除非zone_name对ObstacleTypeManager来说有其他问题
        try
        {
            obstacle_manager_.get_type_info(zone_name);
            // 如果此时类型存在，说明是并发添加，可以继续添加位置信息
        }
        catch (const std::runtime_error &)
        {
            return std::make_pair(false, conflicting_flight_ids); // 类型仍然不存在，说明添加确实失败了
        }
    }

    // 添加禁飞区的栅格位置
    try
    {
        obstacle_manager_.add_obstacle_positions(zone_name, zone_cells);
    }
    catch (const std::runtime_error &e)
    {
        return std::make_pair(false, conflicting_flight_ids); // 添加位置信息失败
    }

    // 更新内部障碍物表示
    update_internal_obstacles();
    return std::make_pair(true, conflicting_flight_ids); // 禁飞区添加成功
}

// 添加基于栅格坐标的中空多边形禁飞区（使用clipper库的版本）
// @param polygon_vertices_grid: 多边形顶点的栅格坐标列表
// @param zone_name: 禁飞区名称
// @param offset_grids: 多边形偏移距离（栅格数，向外扩展）
// @param boundary_thickness_grids: 边界厚度（栅格数，向内扩展）
// @param planned_paths_map: 已规划的航路信息
// @param conflicting_flight_ids: 输出参数，存储与新禁飞区冲突的航班ID
// @return: 添加成功返回true，否则返回false
std::pair<bool, std::vector<std::string>> Map3D::add_hollow_polygonal_no_fly_zone_grid_clipper(
    const std::vector<std::pair<float, float>> &polygon_vertices_grid,
    const std::string &zone_name,
    float offset_grids,
    int boundary_thickness_grids,
    const std::map<std::string, std::vector<GridNode3D>> &planned_paths_map)
{
    std::vector<std::string> conflicting_flight_ids;
    // 清空冲突航班列表
    conflicting_flight_ids.clear();

    // 1. 检查禁飞区类型是否已存在
    try
    {
        obstacle_manager_.get_type_info(zone_name);
        return std::make_pair(true, conflicting_flight_ids); // 类型已存在，无需添加
    }
    catch (const std::runtime_error &)
    {
        // 类型不存在，继续创建过程
    }

    // 检查多边形顶点数量是否合法
    if (polygon_vertices_grid.size() < 3)
    {
        // std::cerr << "错误：禁飞区'" << zone_name << "'的多边形顶点数少于3个。" << std::endl;
        return std::make_pair(false, conflicting_flight_ids);
    }

    // 检查边界厚度是否合法
    if (boundary_thickness_grids < 1)
    {
        // std::cerr << "错误：禁飞区'" << zone_name << "'的边界厚度必须大于等于1。" << std::endl;
        return std::make_pair(false, conflicting_flight_ids);
    }

    // 2. 使用传入的栅格坐标作为原始多边形
    std::vector<std::pair<float, float>> original_polygon_grid_float = polygon_vertices_grid;

    // 3. 使用Clipper2Lib进行多边形几何偏移处理
    std::vector<std::pair<float, float>> offset_polygon_grid_float;
    const double clipper_scale = 10000.0; // 用于整数转换的缩放因子

    // 创建整数坐标的路径
    Clipper2Lib::Path64 input_clipper_path;
    for (const auto &pt_float : original_polygon_grid_float)
    {
        // 将浮点坐标转换为整数坐标并进行缩放
        input_clipper_path.push_back(Clipper2Lib::Point64(
            static_cast<long long>(pt_float.first * clipper_scale),
            static_cast<long long>(pt_float.second * clipper_scale)));
    }

    // 将栅格偏移距离进行缩放
    double clipper_offset_delta_scaled = static_cast<double>(offset_grids * clipper_scale);

    // 准备进行多边形偏移操作
    Clipper2Lib::Paths64 subject_paths;
    subject_paths.push_back(input_clipper_path);
    Clipper2Lib::Paths64 offset_solution;

    // 仅当偏移距离足够大时才进行偏移处理
    if (std::abs(offset_grids) > 1e-6)
    {
        // 使用Clipper2库进行多边形偏移
        // 使用圆角连接方式，对于圆角连接，斜接限制不太重要但仍设为默认值2.0
        offset_solution = Clipper2Lib::InflatePaths(
            subject_paths,
            clipper_offset_delta_scaled,
            Clipper2Lib::JoinType::Round,  // 使用圆角连接
            Clipper2Lib::EndType::Polygon, // 闭合多边形
            2.0);                          // 斜接限制
    }
    else
    {
        // 偏移距离太小，直接使用原始多边形
        offset_solution = subject_paths;
    }

    // 检查偏移结果是否有效
    if (offset_solution.empty() || offset_solution[0].empty())
    {
        // std::cerr << "警告：禁飞区'" << zone_name << "'的多边形偏移结果为空。" << std::endl;
        try
        {
            // 添加一个空的偏移多边形类型
            obstacle_manager_.add_obstacle_type(
                zone_name,
                "Hollow Polygonal No-Fly Zone (Empty/Offset)",
                generate_current_timestamp_string());
        }
        catch (const std::runtime_error &)
        {
            // 忽略已存在的类型
        }
        // 确保内部状态一致性，即使是空区域
        update_internal_obstacles();
        return std::make_pair(true, conflicting_flight_ids);
    }

    // 取第一个偏移结果路径并转换回浮点坐标
    const Clipper2Lib::Path64 &offset_path_scaled = offset_solution[0];
    for (const auto &pt_scaled : offset_path_scaled)
    {
        // 将缩放的整数坐标转换回浮点栅格坐标
        offset_polygon_grid_float.push_back({static_cast<float>(pt_scaled.x / clipper_scale),
                                             static_cast<float>(pt_scaled.y / clipper_scale)});
    }

    // 检查偏移后的多边形是否退化
    if (offset_polygon_grid_float.size() < 3)
    {
        // std::cerr << "警告：禁飞区'" << zone_name << "'的多边形在处理后顶点数少于3个。" << std::endl;
        try
        {
            // 添加一个退化的多边形类型
            obstacle_manager_.add_obstacle_type(
                zone_name,
                "Hollow Polygonal No-Fly Zone (Degenerate/Offset)",
                generate_current_timestamp_string());
        }
        catch (const std::runtime_error &)
        {
            // 忽略已存在的类型
        }
        update_internal_obstacles();
        return std::make_pair(true, conflicting_flight_ids);
    }

    // 4. 将偏移后的多边形边界光栅化
    std::set<std::pair<int, int>> core_edge_cells_2d;
    for (size_t i = 0; i < offset_polygon_grid_float.size(); ++i)
    {
        // 获取相邻两个顶点构成的边
        const auto &p1 = offset_polygon_grid_float[i];
        const auto &p2 = offset_polygon_grid_float[(i + 1) % offset_polygon_grid_float.size()];
        // 光栅化当前边
        std::set<std::pair<int, int>> segment_cells = rasterize_line_segment(p1.first, p1.second, p2.first, p2.second);
        // 将光栅化结果添加到核心边界集合中
        core_edge_cells_2d.insert(segment_cells.begin(), segment_cells.end());
    }

    // 5. 应用边界厚度（向内扩展）
    std::set<std::pair<int, int>> thick_edge_cells_2d;
    if (boundary_thickness_grids == 1)
    {
        // 边界厚度为1时，直接使用核心边界
        thick_edge_cells_2d = core_edge_cells_2d;
    }
    else
    {
        // boundary_thickness_grids应该是向内的厚度
        // 从核心边界开始，向多边形内部扩展 (boundary_thickness_grids - 1) 层
        // 例如：
        // boundary_thickness_grids=1 -> 只有核心边界
        // boundary_thickness_grids=2 -> 核心边界 + 向内1层
        // boundary_thickness_grids=3 -> 核心边界 + 向内2层

        // 首先添加核心边界
        thick_edge_cells_2d = core_edge_cells_2d;

        // 然后向内扩展额外的层数
        std::set<std::pair<int, int>> current_layer = core_edge_cells_2d;

        for (int layer = 1; layer < boundary_thickness_grids; ++layer)
        {
            std::set<std::pair<int, int>> next_layer;

            // 对当前层的每个单元，向内扩展一层
            for (const auto &cell : current_layer)
            {
                // 检查8个相邻单元
                for (int dy = -1; dy <= 1; ++dy)
                {
                    for (int dx = -1; dx <= 1; ++dx)
                    {
                        if (dx == 0 && dy == 0)
                            continue; // 跳过自身

                        std::pair<int, int> neighbor = {cell.first + dx, cell.second + dy};

                        // 检查邻居是否在地图边界内
                        if (neighbor.first >= 0 && neighbor.first < lon_size_ &&
                            neighbor.second >= 0 && neighbor.second < lat_size_)
                        {
                            // 如果邻居还没有被添加到厚边界中，则添加它
                            if (thick_edge_cells_2d.find(neighbor) == thick_edge_cells_2d.end())
                            {
                                next_layer.insert(neighbor);
                            }
                        }
                    }
                }
            }

            // 将新层添加到厚边界中
            thick_edge_cells_2d.insert(next_layer.begin(), next_layer.end());

            // 为下一次迭代准备
            current_layer = next_layer;

            // 如果没有新的单元可以扩展，提前退出
            if (next_layer.empty())
            {
                break;
            }
        }

        // 确保至少有核心边界
        if (thick_edge_cells_2d.empty() && !core_edge_cells_2d.empty())
        {
            thick_edge_cells_2d = core_edge_cells_2d;
        }
    }

    // 6. 将二维边界扩展到三维空间并收集禁飞区单元
    std::set<Point3D> zone_cells;
    for (const auto &cell_2d : thick_edge_cells_2d)
    {
        int lon_idx = cell_2d.first;
        int lat_idx = cell_2d.second;

        // 检查栅格坐标是否在地图边界内
        if (lon_idx < 0 || lon_idx >= lon_size_ || lat_idx < 0 || lat_idx >= lat_size_)
        {
            continue;
        }

        // 在每个有效的水平位置上，将所有高度层添加到禁飞区
        for (int alt_idx = 0; alt_idx < alt_size_; ++alt_idx)
        {
            zone_cells.insert({lon_idx, lat_idx, alt_idx});
        }
    }

    // 检查是否成功生成了禁飞区单元
    if (zone_cells.empty())
    {
        // std::cerr << "警告：禁飞区'" << zone_name << "'处理后未生成任何有效栅格。" << std::endl;
        try
        {
            // 添加一个空的最终多边形类型
            obstacle_manager_.add_obstacle_type(
                zone_name,
                "Hollow Polygonal No-Fly Zone (Empty/Final)",
                generate_current_timestamp_string());
        }
        catch (const std::runtime_error &)
        {
            // 忽略已存在的类型
        }
        update_internal_obstacles();
        return std::make_pair(true, conflicting_flight_ids);
    }

    // 7. 检查与已有航路的冲突
    check_path_conflicts(zone_cells, planned_paths_map, conflicting_flight_ids);

    // 8. 将禁飞区添加到障碍物管理器
    try
    {
        // 创建多边形禁飞区几何信息（使用偏移后的多边形）
        auto geometry = std::make_unique<PolygonalNFZGeometry>(offset_polygon_grid_float);

        // 添加正常的中空多边形禁飞区类型（带几何信息）
        obstacle_manager_.add_nfz_type(
            zone_name,
            "Hollow Polygonal No-Fly Zone",
            generate_current_timestamp_string(),
            std::move(geometry));
    }
    catch (const std::runtime_error &)
    {
        // 如果添加类型失败（例如由于并发操作已经存在），尝试确认是否已存在
        // 如果get_type_info也失败，则说明确实是问题
        try
        {
            obstacle_manager_.get_type_info(zone_name);
        }
        catch (const std::runtime_error &)
        {
            return std::make_pair(false, conflicting_flight_ids); // 添加失败且类型不存在，确认是真正的错误
        }
    }

    // 添加禁飞区位置信息
    try
    {
        obstacle_manager_.add_obstacle_positions(zone_name, zone_cells);
    }
    catch (const std::runtime_error &e)
    {
        // std::cerr << "错误：添加禁飞区'" << zone_name << "'的位置信息失败：" << e.what() << std::endl;
        return std::make_pair(false, conflicting_flight_ids); // 添加位置信息失败
    }

    // 更新内部障碍物状态并返回成功
    update_internal_obstacles();
    return std::make_pair(true, conflicting_flight_ids); // 禁飞区添加成功
}

// 添加中空多边形禁飞区
// @param polygon_vertices_geo: 多边形顶点的地理坐标列表
// @param zone_name: 禁飞区名称
// @param offset_meters_b: 多边形偏移距离（米，向外扩展）
// @param boundary_thickness_grids: 边界厚度（栅格数，向内扩展）
// @param planned_paths_map: 已规划的航路信息
// @return: 返回一个pair，第一个元素表示是否成功，第二个元素是冲突的航班ID列表
std::pair<bool, std::vector<std::string>> Map3D::add_hollow_polygonal_no_fly_zone(
    const std::vector<GeoCoordinate> &polygon_vertices_geo,
    const std::string &zone_name,
    double offset_meters_b,
    int boundary_thickness_grids,
    const std::map<std::string, std::vector<GridNode3D>> &planned_paths_map)
{
    std::vector<std::string> conflicting_flight_ids; // 在函数开始处声明
    // 清空冲突航班列表
    conflicting_flight_ids.clear();

    // 1. 检查禁飞区类型是否已存在
    try
    {
        obstacle_manager_.get_type_info(zone_name);
        return std::make_pair(true, conflicting_flight_ids); // 类型已存在，无需添加
    }
    catch (const std::runtime_error &)
    {
        // 类型不存在，继续创建过程
    }

    // 检查多边形顶点数量是否合法
    if (polygon_vertices_geo.size() < 3)
    {
        // std::cerr << "错误：禁飞区'" << zone_name << "'的多边形顶点数少于3个。" << std::endl;
        return std::make_pair(false, conflicting_flight_ids);
    }

    // 检查边界厚度是否合法
    if (boundary_thickness_grids < 1)
    {
        // std::cerr << "错误：禁飞区'" << zone_name << "'的边界厚度必须大于等于1。" << std::endl;
        return std::make_pair(false, conflicting_flight_ids);
    }

    // 2. 将地理坐标转换为浮点栅格坐标
    std::vector<std::pair<float, float>> polygon_vertices_grid;
    polygon_vertices_grid.reserve(polygon_vertices_geo.size());

    // 转换所有顶点坐标
    for (const auto &geo_coord : polygon_vertices_geo)
    {
        GridPoint grid_pt_tuple = converter_.geographic_to_grid(geo_coord);
        polygon_vertices_grid.push_back({std::get<0>(grid_pt_tuple),   // X坐标
                                         std::get<1>(grid_pt_tuple)}); // Y坐标
    }

    // 3. 将米制偏移距离转换为栅格单位
    float offset_grids = converter_.convert_radius_meters_to_grids_using_lat_resolution(
        static_cast<float>(offset_meters_b));

    // 4. 调用栅格版本的函数（新版本，不使用clipper）
    // 注意：这里需要接收 add_hollow_polygonal_no_fly_zone_grid 返回的 pair
    // 然后将 conflicting_flight_ids 从 pair 中提取出来，如果需要的话
    // 但由于此函数也返回 pair，可以直接返回其结果
    return add_hollow_polygonal_no_fly_zone_grid(
        polygon_vertices_grid,
        zone_name,
        offset_grids,
        boundary_thickness_grids,
        planned_paths_map);
}

// 添加基于栅格坐标的中空多边形禁飞区（新版本，不使用clipper）
// @param polygon_vertices_grid: 多边形顶点的栅格坐标列表
// @param zone_name: 禁飞区名称
// @param offset_grids: 多边形偏移距离（栅格数，向外扩展）
// @param boundary_thickness_grids: 边界厚度（栅格数，向内扩展）
// @param planned_paths_map: 已规划的航路信息
// @return: 添加成功返回true，否则返回false
std::pair<bool, std::vector<std::string>> Map3D::add_hollow_polygonal_no_fly_zone_grid(
    const std::vector<std::pair<float, float>> &polygon_vertices_grid,
    const std::string &zone_name,
    float offset_grids,
    int boundary_thickness_grids,
    const std::map<std::string, std::vector<GridNode3D>> &planned_paths_map)
{
    std::vector<std::string> conflicting_flight_ids;
    conflicting_flight_ids.clear();

    // 1. 检查禁飞区类型是否已存在
    try
    {
        obstacle_manager_.get_type_info(zone_name);
        return std::make_pair(true, conflicting_flight_ids); // 类型已存在，无需添加
    }
    catch (const std::runtime_error &)
    {
        // 类型不存在，继续创建过程
    }

    // 检查多边形顶点数量是否合法
    if (polygon_vertices_grid.size() < 3)
    {
        return std::make_pair(false, conflicting_flight_ids);
    }

    // 检查边界厚度是否合法
    if (boundary_thickness_grids < 1)
    {
        return std::make_pair(false, conflicting_flight_ids);
    }

    // 2. 使用传入的栅格坐标作为原始多边形
    std::vector<std::pair<float, float>> original_polygon_grid_float = polygon_vertices_grid;

    // 3. 进行多边形扩展（如果需要）
    std::vector<std::pair<float, float>> expanded_polygon_grid_float;
    if (std::abs(offset_grids) > 1e-6)
    {
        expanded_polygon_grid_float = expand_polygon(original_polygon_grid_float, offset_grids);
    }
    else
    {
        expanded_polygon_grid_float = original_polygon_grid_float;
    }

    // 检查扩展后的多边形是否有效
    if (expanded_polygon_grid_float.size() < 3)
    {
        try
        {
            obstacle_manager_.add_obstacle_type(
                zone_name,
                "Hollow Polygonal No-Fly Zone (Degenerate/Expanded)",
                generate_current_timestamp_string());
        }
        catch (const std::runtime_error &)
        {
            // 忽略已存在的类型
        }
        update_internal_obstacles();
        return std::make_pair(true, conflicting_flight_ids);
    }

    // 4. 提取多边形边界点
    std::vector<std::pair<int, int>> boundary_points_2d = extract_polygon_boundary(expanded_polygon_grid_float, boundary_thickness_grids);

    // 5. 生成3D边界点
    std::set<Point3D> zone_cells = generate_boundary_points_3d(boundary_points_2d, alt_size_);

    // 检查是否成功生成了禁飞区单元
    if (zone_cells.empty())
    {
        try
        {
            obstacle_manager_.add_obstacle_type(
                zone_name,
                "Hollow Polygonal No-Fly Zone (Empty/Final)",
                generate_current_timestamp_string());
        }
        catch (const std::runtime_error &)
        {
            // 忽略已存在的类型
        }
        update_internal_obstacles();
        return std::make_pair(true, conflicting_flight_ids);
    }

    // 6. 检查与已有航路的冲突
    check_path_conflicts(zone_cells, planned_paths_map, conflicting_flight_ids);

    // 7. 将禁飞区添加到障碍物管理器
    try
    {
        // 创建多边形禁飞区几何信息（使用扩展后的多边形）
        auto geometry = std::make_unique<PolygonalNFZGeometry>(expanded_polygon_grid_float);

        // 添加正常的中空多边形禁飞区类型（带几何信息）
        obstacle_manager_.add_nfz_type(
            zone_name,
            "Hollow Polygonal No-Fly Zone",
            generate_current_timestamp_string(),
            std::move(geometry));
    }
    catch (const std::runtime_error &)
    {
        try
        {
            obstacle_manager_.get_type_info(zone_name);
        }
        catch (const std::runtime_error &)
        {
            return std::make_pair(false, conflicting_flight_ids);
        }
    }

    // 添加禁飞区位置信息
    try
    {
        obstacle_manager_.add_obstacle_positions(zone_name, zone_cells);
    }
    catch (const std::runtime_error &e)
    {
        return std::make_pair(false, conflicting_flight_ids);
    }

    // 更新内部障碍物状态并返回成功
    update_internal_obstacles();
    return std::make_pair(true, conflicting_flight_ids);
}

std::pair<bool, std::vector<std::string>> Map3D::add_solid_cylindrical_no_fly_zone(
    double center_lat_deg, double center_lon_deg, double radius_meters,
    const std::string &zone_name,
    int buffer_distance_meters,
    const std::map<std::string, std::vector<GridNode3D>> &planned_paths_map, // 修改类型
    int boundary_thickness_grids)
{
    std::vector<std::string> conflicting_flight_ids;
    conflicting_flight_ids.clear(); // 清空输出参数

    // 首先检查禁飞区类型是否已存在 (优化，避免不必要的转换)
    bool type_existed_initially = false;
    try
    {
        obstacle_manager_.get_type_info(zone_name);
        type_existed_initially = true;
    }
    catch (const std::runtime_error &)
    {
        // 类型不存在，继续执行添加逻辑
    }

    if (type_existed_initially)
    {
        // 类型已存在，不进行冲突检测，直接返回成功
        return std::make_pair(true, conflicting_flight_ids);
    }

    double R_eff_meters = radius_meters + static_cast<double>(buffer_distance_meters);

    // Convert Geographic Center to Float Grid Center
    GridPoint center_grid_float_tuple = converter_.geographic_to_grid({center_lat_deg, center_lon_deg, 0.0});
    float center_lon_grid_float = std::get<0>(center_grid_float_tuple); // 经度对应X轴
    float center_lat_grid_float = std::get<1>(center_grid_float_tuple); // 纬度对应Y轴

    // Convert Effective Radius (meters) to Grid Radius (float)
    // Using the method based on latitude resolution as discussed
    float R_eff_grid_float = converter_.convert_radius_meters_to_grids_using_lat_resolution(R_eff_meters);

    if (R_eff_grid_float < 0.5f && boundary_thickness_grids <= 0)
    { // If radius is less than half a grid cell for a solid cylinder
      // std::cout << "Warning: Effective radius for zone '" << zone_name << "' is very small (" << R_eff_grid_float << " grids)." << std::endl;
      // Depending on the logic in add_solid_cylindrical_no_fly_zone_grid, this might result in 0 or 1 cell.
      // If it's truly zero, we might want to return true (nothing to add, no conflict).
    }

    std::pair<bool, std::vector<std::string>> result = add_solid_cylindrical_no_fly_zone_grid(
        center_lon_grid_float, center_lat_grid_float,
        R_eff_grid_float,
        zone_name,
        planned_paths_map, // 传递 map
        boundary_thickness_grids);
    return result;
}

// 私有辅助方法实现
// @brief 检查禁飞区与已规划航路的冲突情况
// @param zone_cells: 禁飞区的栅格单元集合
// @param planned_paths_map: 已规划的航路信息（航班ID到路径点序列的映射）
// @param conflicting_flight_ids_out: 输出参数，存储所有与禁飞区冲突的航班ID
// @note: 调用者需要在传入conflicting_flight_ids_out前清空它，或在此函数中清空
void Map3D::check_path_conflicts(
    const std::set<Point3D> &zone_cells,
    const std::map<std::string, std::vector<GridNode3D>> &planned_paths_map,
    std::vector<std::string> &conflicting_flight_ids_out) const
{
    // 遍历所有已规划航路
    bool conflict_found_for_current_flight = false;
    for (const auto &pair : planned_paths_map)
    {
        const std::string &flight_id = pair.first;               // 航班ID
        const std::vector<GridNode3D> &path_nodes = pair.second; // 航路点序列
        conflict_found_for_current_flight = false;               // 重置当前航班的冲突标志

        // 检查航路上的每个节点
        for (const auto &node : path_nodes)
        {
            // 将路径节点坐标转换为整数栅格索引
            int path_lon_idx = static_cast<int>(std::round(node.x)); // 经度索引
            int path_lat_idx = static_cast<int>(std::round(node.y)); // 纬度索引
            int path_alt_idx = static_cast<int>(std::round(node.z)); // 高度索引

            // 确保路径节点坐标在地图边界内
            path_lon_idx = std::max(0, std::min(lon_size_ - 1, path_lon_idx));
            path_lat_idx = std::max(0, std::min(lat_size_ - 1, path_lat_idx));
            path_alt_idx = std::max(0, std::min(alt_size_ - 1, path_alt_idx));

            // 检查是否与禁飞区有交集
            if (zone_cells.count({path_lon_idx, path_lat_idx, path_alt_idx}))
            {
                conflict_found_for_current_flight = true;
                break; // 发现冲突后立即退出当前航路检查
            }
        }

        // 如果当前航班有冲突，将其ID添加到冲突列表
        if (conflict_found_for_current_flight)
        {
            conflicting_flight_ids_out.push_back(flight_id);
        }
    }
}

// 删除指定的禁飞区
// @brief 从地图中移除特定名称的禁飞区
// @param zone_name: 要删除的禁飞区名称
// @return: 删除成功返回true，否则返回false
bool Map3D::remove_no_fly_zone(const std::string &zone_name)
{
    try
    {
        // 检查禁飞区是否存在
        try
        {
            obstacle_manager_.get_type_info(zone_name);
        }
        catch (const std::runtime_error &)
        {
            // 禁飞区不存在，视为删除成功
            return true;
        }

        // 从障碍物管理器中删除禁飞区
        obstacle_manager_.remove_obstacle_type(zone_name);

        // 更新地图内部状态
        update_internal_obstacles();

        return true; // 删除成功
    }
    catch (const std::exception &e)
    {
        // 删除过程中发生错误
        // std::cerr << "错误：删除禁飞区'" << zone_name << "'失败：" << e.what() << std::endl;
        return false;
    }
}

// 检查指定二维点是否位于任何禁飞区内
// @brief 检查给定的二维栅格坐标点是否位于任何已定义的禁飞区内
// @param point_lat_lon: 要检查的点的 (lat_idx, lon_idx) 网格坐标
// @return: 如果点位于任何NFZ内部，则返回该NFZ的名称，及时break；如果点不在任何NFZ内部，则返回空字符串
std::string Map3D::is_point_inside_any_nfz_2d(const std::pair<int, int> &point_lat_lon) const
{
    int lat_idx = point_lat_lon.first;
    int lon_idx = point_lat_lon.second;

    // 检查坐标是否在地图边界内
    if (lon_idx < 0 || lon_idx >= lon_size_ || lat_idx < 0 || lat_idx >= lat_size_)
    {
        return ""; // 超出地图边界，不在任何禁飞区内
    }

    // 使用障碍物管理器的几何检查方法
    // 将整数坐标转换为浮点数坐标进行几何计算
    return obstacle_manager_.is_point_inside_any_nfz_2d(static_cast<float>(lon_idx), static_cast<float>(lat_idx));
}

// 调试函数：获取禁飞区几何信息
std::string Map3D::debug_nfz_geometry_info() const
{
    return obstacle_manager_.debug_nfz_geometry_info();
}
